package net.summerfarm.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.qiNiu.UploadTokenFactory;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.datacollect.UnCollectByAspect;
import net.summerfarm.mall.common.util.IPUtil;
import net.summerfarm.mall.common.util.IpWhiteListUtil;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.common.util.WeChatUtils;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.model.domain.MerchantExt;
import net.summerfarm.mall.model.dto.login.MerchantLoginDto;
import net.summerfarm.mall.model.input.DataStorageInput;
import net.summerfarm.mall.model.input.DataStorageQueryInput;
import net.summerfarm.mall.model.input.DistributionRulesGetInput;
import net.summerfarm.mall.model.input.product.CommodityInventoryInput;
import net.summerfarm.mall.model.input.product.ProductSearchInput;
import net.summerfarm.mall.model.input.product.ProductSpecificationQueryInput;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.model.vo.product.CommodityInventoryVO;
import net.summerfarm.mall.service.*;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

/**
 * @Package: net.summerfarm.controller
 * @Description: 商城
 * @author: <EMAIL>
 * @Date: 2016/10/8
 */
@Slf4j
@Api(tags = "商城控制类")
@RestController
public class MallController{

    @Resource
    private MerchantService merchantService;
    @Resource
    private InventoryService inventoryService;
    @Resource
    private ArrivalNoticeService arrivalNoticeService;
    @Resource
    AreaService areaService;
    @Resource
    private DeliveryService deliveryService;
    @Resource
    private DistributionRulesService distributionRulesService;

    @NacosValue(value = "${mall.product.list.pageSize:6}", autoRefreshed = true)
    private int defaultPageSize;

    @Resource
    private HttpServletRequest request;

    /**
     * 登录接口的船新版本
     * @param type 登录类型：0、公众号 1、小程序
     * @param code code
     * @return 登录信息
     */
    @ApiOperation(value = "微信登录",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type",value = "登录类型",paramType = "query",defaultValue = "1",required = true),
            @ApiImplicitParam(name = "code",value = "临时码",paramType = "query",required = true),
            @ApiImplicitParam(name = "phone",value = "phone",paramType = "query",required = false),
            @ApiImplicitParam(name = "sign",value = "签名",paramType = "query",required = false),
    })
    @RequestMapping(value = {"/openid","/summerfarm-mall/openid"},method = RequestMethod.GET)
    public AjaxResult getOpenid(Integer type, String code, String phone, String sign) {
        return merchantService.login(type, code, phone, sign);
    }



    /**
     * V2登录接口
     * @return 登录信息
     */
    @RequestMapping(value = {"/loginV2"},method = RequestMethod.POST)
    public AjaxResult getOpenid(@RequestBody MerchantLoginDto merchantLoginDto) {
        return merchantService.loginV2(merchantLoginDto);
    }


    /**
     * 查看登录信息
     * @return 登录信息
     */
    @ApiOperation(value = "获取登录信息",httpMethod = "GET")
    @RequestMapping(value = "/login-info",method = RequestMethod.GET)
    @RequiresAuthority
    public AjaxResult<LoginResponseVO> getLoginInfo() {
        return merchantService.getLoginInfo();
    }

    /**
     * 微信容器打开页面url获取
     *
     * @param url
     * @param scope
     * @return
     * @throws UnsupportedEncodingException
     */
    @ApiOperation(value = "微信容器打开页面url获取", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "url", value = "url地址", paramType = "query", required = true),
            @ApiImplicitParam(name = "scope", value = "授权方式,snsapi_base : 静默授权,snsapi_userinfo : 主动授权", paramType = "query", defaultValue = "snsapi_base")
    })
    @RequestMapping(value = "/register/wechat-code", method = RequestMethod.GET)
    public String getWechatCode(String url, String scope) {
        // 请求链接：https://preh5.summerfarm.net/register/wechat-code?url=%2Fhome.html%23%2Floading%3Ft%3D1745977244840%26&scope=snsapi_userinfo
        // 这里的URL参数其实只是URL path，不包含host
        String domain = getDomain(request);
        log.info("domain: {}", domain);
        return WeChatUtils.getWeChatCode(url, scope, domain);
    }

    private static String getDomain(HttpServletRequest request) {
        String domain = request.getRequestURL().toString().replace(request.getRequestURI(), "");
        log.info("Using domain from request header: {}", domain);
        return domain;
    }


    /**
     * 微信容器打开页面url获取（for顺鹿达）
     * @param url
     * @param scope
     * @return
     * @throws UnsupportedEncodingException
     */
    @ApiOperation(value = "微信容器打开页面url获取（for顺鹿达）",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "url",value = "url地址",paramType = "query",required = true),
            @ApiImplicitParam(name = "scope",value = "授权方式,snsapi_base : 静默授权,snsapi_userinfo : 主动授权",paramType = "query",defaultValue = "snsapi_base")
    })
    @RequestMapping(value = "/register/pop-wechat-code", method = RequestMethod.GET)
    public String getPopWechatCode(String url, String scope) {
        // 请求链接：https://preh5.summerfarm.net/register/wechat-code?url=%2Fhome.html%23%2Floading%3Ft%3D1745977244840%26&scope=snsapi_userinfo
        // 这里的URL参数其实只是URL path，不包含host
        String domain = getDomain(request);
        log.info("Using POP domain from request header: {}", domain);
        return WeChatUtils.getPopWeChatCode(url, scope, domain);
    }

    /**
     * 获取系统时间
     * @return
     */
    @ApiOperation(value = "获取系统时间",httpMethod = "GET")
    @RequestMapping(value = "/now",method = RequestMethod.GET)
    public AjaxResult now() {
        return AjaxResult.getOK(System.currentTimeMillis());
    }


    /**
     * 获取配送信息
     * @param contactId
     * @return
     */
    @ApiOperation(value = "获取配送信息",httpMethod = "GET")
    @RequestMapping(value = "/delivery",method = RequestMethod.GET)
    @RequiresAuthority
    public AjaxResult delivery(Long contactId) {
        DeliveryInfoVO deliveryInfoVO = deliveryService.getDeliveryInfo(contactId);
        MerchantExt merchantExt = areaService.getFreeDay(RequestHolder.getMId());
        if (StringUtils.isNotBlank(merchantExt.getFreeDay())) {
            deliveryInfoVO.setFreeDeliveryWeek(merchantExt.getFreeDay());
        }
        return AjaxResult.getOK(deliveryInfoVO);
    }


    @ApiOperation(value = "图片上传",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type",value = "picture_path:sku列表图,detailpicture1_path:sku商品详情图,detailpicture2_path:放大图2," +
                    "detailpicture3_path:放大图3,instroduction:商品详情介绍,after_sale:售后图片",
                    paramType = "query",defaultValue = "after_sale",required = true),
            @ApiImplicitParam(name = "fileName",value = "图片名称",paramType = "query",required = true)
    })
    @RequestMapping(value = "/pic/upload-token", method = RequestMethod.GET)
    @RequiresAuthority
    public AjaxResult getUploadToken(String type, String fileName) {
        return AjaxResult.getOK(UploadTokenFactory.createToken(type,fileName,3600));
    }

    @RequestMapping(value = "/arrival-notice", method = RequestMethod.POST)
    @RequiresAuthority
    public AjaxResult arrivalNotice(@RequestParam String sku,String pdName,String weight,Integer type, Integer num) {
        return arrivalNoticeService.insert(sku,pdName,weight,type,num);
    }

    /**
     * 首页商品查询
     * @param pageIndex
     * @param pageSize
     * @param categoryIds
     * @param pdName
     * @param skus
     * @param couponId
     * @return
     */
    @ApiOperation(value = "首页商品查询",httpMethod = "GET",response = ProductInfoVO.class)
    @RequestMapping(value = "/product-coupon/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectByCouponId(@PathVariable int pageIndex, @PathVariable int pageSize,String categoryIds,String pdName,String skus, Long couponId){
        return inventoryService.selectByCouponId(pageIndex,pageSize,pdName,categoryIds,skus, couponId);
    }
    /**
     * 商品搜索建议
     * @param pdName
     * @return
     */
    @ApiOperation(value = "商品搜索建议",httpMethod = "GET",response = EsProductVO.class)
    @GetMapping("/product/suggest")
    public AjaxResult querySuggest(String pdName){
        return AjaxResult.getOK(inventoryService.querySuggestV2(pdName));
    }

    @ApiOperation(value = "心跳",httpMethod = "GET")
    @RequestMapping(value = "/ok", method = RequestMethod.GET)
    @UnCollectByAspect
    public String OK() {
        if (Global.HEARTBEAT_FLAG) {
            return "success";
        }
        HttpServletResponse response = RequestHolder.getResponse();
        response.setStatus(403);
        return "系统关闭，请联系系统管理员";
    }

    @ApiOperation(value = "活动 查询固定sku信息", httpMethod = "GET")
    @GetMapping("/temporary/fix/{pageIndex}/{pageSize}")
    @RequiresAuthority
    public AjaxResult selectSkuList(@PathVariable int pageIndex, @PathVariable int pageSize,String tableName){
        return inventoryService.selectSkuList(pageIndex, pageSize,tableName);
    }


    /**
     * 批量获取配送规则 根据sku集合获取当前sku运费规则
     * @param input
     * @return DistributionRulesVO
     */
    @PostMapping("/query/distribution-rules")
    @RequiresAuthority
    public CommonResult<List<DistributionRulesVO>> getDistributionRules(@RequestBody @Valid DistributionRulesGetInput input) {
        return CommonResult.ok(distributionRulesService.getDistributionRulesBySkus(input));
    }

    /**
     * 批量获取sku库存数据
     * @param input
     * @return DistributionRulesVO
     */
    @PostMapping("/query/commodity-inventory")
    @RequiresAuthority
    public CommonResult<List<CommodityInventoryVO>> getCommodityInventory(@RequestBody @Valid CommodityInventoryInput input) {
        return CommonResult.ok(inventoryService.getCommodityInventory(input));
    }


    /**
     * 分类
     * @return
     */
    @ApiOperation(value = "首页商品查询",httpMethod = "GET",response = ProductInfoVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页数",paramType = "path",defaultValue = "1",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",defaultValue = "10",required = true),
            @ApiImplicitParam(name = "frontCategoryId",value = "前台类目id",paramType = "query"),
            @ApiImplicitParam(name = "pdName",value = "商品名称",paramType = "query"),
            @ApiImplicitParam(name = "mId",value = "商户id",paramType = "query"),
            @ApiImplicitParam(name = "addOn",value = "商品性质",paramType = "query"),
            @ApiImplicitParam(name = "queryStr", value = "查询pdName或sku", paramType = "query")
    })
    @RequestMapping(value = "/product/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult product(@PathVariable int pageIndex, @PathVariable int pageSize, ProductSearchInput input) {
        String ip = IPUtil.getIpAddress(RequestHolder.getRequest());
        if (!IpWhiteListUtil.check(ip)) {
            input.setMId(null);
        }
        if (pageSize <= defaultPageSize) {
            pageSize = defaultPageSize;
            log.info("已替换成defaultPageSize：{}", defaultPageSize);
        }
        return AjaxResult.getOK(inventoryService.selectHomeProductVoV2(pageIndex, pageSize, input));
    }

    /**
     * 商详
     * @param pdId
     * @return
     */
    @ApiOperation(value = "商品信息查询",httpMethod = "GET")
    @ApiImplicitParam(name = "pdId",value = "商品id",paramType = "path",defaultValue = "3",required = true)
    @RequestMapping(value = "/product-info/{pdId}", method = RequestMethod.GET)
    public AjaxResult productInfo(@PathVariable long pdId) {
        return inventoryService.selectProductInfo(pdId);
    }

    /**
     * 根据pdId批量获取当前商品是单规格还是多规格
     * @param input
     * @return
     */
    @RequestMapping(value = "/query/product-spec", method = RequestMethod.POST)
    public CommonResult<List<ProductVO>> queryProductSpec(@RequestBody ProductSpecificationQueryInput input) {
        return CommonResult.ok(inventoryService.queryProductSpec(input));
    }

    /**
     * 临时储存数据
     */
    @RequestMapping(value = "/upsert/temporary-data-storage", method = RequestMethod.POST)
    @RequiresAuthority
    public CommonResult<Void> upsertTemporaryData(@RequestBody @Validated DataStorageInput input) {
        merchantService.upsertTemporaryData(input);
        return CommonResult.ok();
    }

    /**
     * 获取临时储存数据
     */
    @RequestMapping(value = "/query/temporary-data-storage", method = RequestMethod.POST)
    @RequiresAuthority
    public CommonResult<JSONObject> queryTemporaryData(@RequestBody @Validated DataStorageQueryInput input) {
        return CommonResult.ok(merchantService.getTemporaryData(input));
    }
}
