package net.summerfarm.mall.controller;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.input.product.*;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.service.*;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Package: net.summerfarm.controller
 * @Description: 商城
 * @author: <EMAIL>
 * @Date: 2016/10/8
 */
@Slf4j
@RestController
@RequestMapping(value = "mall/sku")
public class MallSkuController {

    @Resource
    private MerchantService merchantService;
    @Resource
    private SkuQueryService skuQueryService;
    @Resource
    private InventoryService inventoryService;

    @NacosValue(value = "${mall.product.list.pageSize:6}", autoRefreshed = true)
    private int defaultPageSize;



    /**
     * 搜索
     *
     * @return
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public CommonResult<PageInfo<ProductInfoVO>> productPage(@RequestBody ProductQueryInput input) {
        if (input.getPageSize () <= defaultPageSize) {
            input.setPageSize (defaultPageSize);
            log.info ("已替换成defaultPageSize：{}", defaultPageSize);
        }
        return CommonResult.ok (skuQueryService.selectHomeProductVo(input));
    }

    /**
     * 搜索 筛选项
     *
     * @return
     */
    @RequestMapping(value = "/condition", method = RequestMethod.POST)
    public CommonResult<List<SearchConditionVO>> condition(@RequestBody ProductQueryInput input) {
        String pdName = input.getTitleSuggest ();
        Integer areaNo = RequestHolder.getMerchantArea().getAreaNo();
        String key = areaNo + pdName;
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject ();
        if(merchantSubject!=null){
            Integer adminId = merchantSubject.getAdminId ();
            key  = adminId + key;
        }
        return CommonResult.ok (skuQueryService.selectQueryCondition(key));
    }
    /**
     *
     *  可能想买
     * @return
     */
    @RequestMapping(value = "/wantBuy", method = RequestMethod.POST)
    public CommonResult<List<String>> wantBuy(@RequestBody ProductQueryInput input) {
        return CommonResult.ok (merchantService.wantBuy(input));
    }

    /**
     *
     *  mimi榜单
     * @return
     */
    @RequestMapping(value = "/hotList", method = RequestMethod.POST)
    public CommonResult<List<ProductInfoVO>> hotList(@RequestBody HotListSearchInput input) {
        return CommonResult.ok (inventoryService.getHotList(input));
    }

}
