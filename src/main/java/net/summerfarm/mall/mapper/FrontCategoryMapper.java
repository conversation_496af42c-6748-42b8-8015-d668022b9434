package net.summerfarm.mall.mapper;
import java.util.ArrayList;
import java.util.Collection;

import net.summerfarm.mall.model.domain.FrontCategory;
import net.summerfarm.mall.model.domain.category.FrontCategoryMappingDO;
import net.summerfarm.mall.model.vo.FrontCategoryCountVO;
import net.summerfarm.mall.model.vo.FrontCategoryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FrontCategoryMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(FrontCategory record);

    int insertSelective(FrontCategory record);

    FrontCategory selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(FrontCategory record);

    int updateByPrimaryKey(FrontCategory record);

    /**
     * 根据映射类目查询前台类目
     * @param categoryIds
     * @return
     */
    List<FrontCategoryVO> selectByCategoryIds(@Param("categoryIds") List<Integer> categoryIds, @Param("areaNo") Integer areaNo);
    List<FrontCategoryCountVO> selectByCategoryIdsByCategoryIds(@Param("categoryIds") List<Integer> categoryIds, @Param("areaNo") Integer areaNo);

    /**
     * 查询所有一级类目
     * @param  areaNo
     * @return
     */
    List<FrontCategoryVO> selectParentFrontCategory(@Param("areaNo") Integer areaNo);

    /**
     * 查询子目录
     * @param parentId
     * @return
     */
    List<FrontCategory> selectByParentId(@Param("parentId") Integer parentId);

    List<Integer> selectBackCategory(@Param("areaNo") Integer areaNo, @Param("frontCategoryId") Integer frontCategoryId);

    List<FrontCategoryVO> selectByIds(@Param("ids")List<Integer> ids, @Param("areaNo") Integer areaNo);

    /**
     * 根据前台一级类目查询区域展示的后台类目
     * @param ids
     * @param areaNo
     * @return
     */
    List<Integer> selectCategoryByParentIds(@Param("ids")List<Integer> ids, @Param("areaNo") Integer areaNo);

    /**
     * 根据id列表查询
     *
     * @param idList 类目id
     * @return 类目列表
     */
    List<FrontCategoryVO> selectByIdList(@Param("list") List<Integer> idList);

    /**
     * 根据三级类目查询前台类目
     * @param categoryIds 三级类目
     * @return 三级类目 和 前台一级类目映射
     */
    List<FrontCategoryMappingDO> selectCategoryMappingByCategoryIds(@Param("categoryIds") List<Integer> categoryIds);

    /**
     * 查询直接关联sku类型的前台类目
     * @param areaNo
     * @return
     */
    List<FrontCategoryVO> selectBySkuRelateType(@Param("areaNo")Integer areaNo);
}