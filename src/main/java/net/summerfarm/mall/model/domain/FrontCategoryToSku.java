package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * front_category_to_category
 * <AUTHOR>
@Data
public class FrontCategoryToSku implements Serializable {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 前台类目id
     */
    private Long frontCategoryId;

    /**
     * sku
     */
    private String sku;

    /**
     * pdName
     */
    private String pdName;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updater;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;


    private static final long serialVersionUID = 1L;
}