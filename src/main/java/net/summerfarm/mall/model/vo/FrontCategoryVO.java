package net.summerfarm.mall.model.vo;

import lombok.Data;
import lombok.ToString;
import net.summerfarm.mall.model.domain.Category;
import net.summerfarm.mall.model.domain.FrontCategory;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-05-29
 * @description
 */
@Data
@ToString(callSuper = true)
public class FrontCategoryVO extends FrontCategory {
    /**
     * 类目是否展示
     */
    private Integer display;

    private Integer priority;
    /**
     * 子类目
     */
    private List<FrontCategoryVO> childList;
    /**
     * 后台类目
     */
    private List<Category> categoryList;
}
