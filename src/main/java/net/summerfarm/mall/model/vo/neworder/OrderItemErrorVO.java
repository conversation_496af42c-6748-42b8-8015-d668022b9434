package net.summerfarm.mall.model.vo.neworder;

import java.io.Serializable;
import java.time.LocalDate;
import lombok.Data;
import net.summerfarm.mall.enums.order.OrderItemErrorEnum;

/**
 * @author: <EMAIL>
 * @create: 2023/9/28
 */
@Data
public class OrderItemErrorVO implements Serializable {

    /**
     * sku code
     */
    private String sku;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 规格
     */
    private String weight;

    /**
     * 图片
     */
    private String picturePath;

    /**
     * 商品类型：0、普通商品 1、赠品 2、换购品
     */
    private Integer productType;

    /**
     * 剩余库存数量
     */
    private Integer quantity;

    /**
     * 下单失败原因，0 已下架 1 库存不足 2 配送时间变化
     * @see OrderItemErrorEnum
     */
    private Integer reason;

    /**
     * 预计送达时间（reason为2 的时候使用）
     */
    private LocalDate predictDeliveryDate;


}
