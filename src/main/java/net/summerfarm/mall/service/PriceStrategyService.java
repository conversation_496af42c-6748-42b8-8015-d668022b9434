package net.summerfarm.mall.service;

import net.summerfarm.enums.PriceStrategyTypeEnum;
import net.summerfarm.mall.model.domain.PriceStrategy;

import java.math.BigDecimal;

public interface PriceStrategyService {

    /**
     * 计算价格并触发审批
     *
     * @param priceStrategy 价格策略
     * @param costPrice     成本价
     * @param originalPrice 原价
     * @return 计算后的价格
     */
    BigDecimal calcStrategyPrice(PriceStrategy priceStrategy, BigDecimal costPrice, BigDecimal originalPrice);
}
