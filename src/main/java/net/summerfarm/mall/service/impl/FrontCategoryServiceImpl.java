package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import net.summerfarm.enums.MajorPriceMallShowEnum;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.FrontCategoryRelateTypeEnum;
import net.summerfarm.mall.mapper.CategoryMapper;
import net.summerfarm.mall.mapper.FrontCategoryMapper;
import net.summerfarm.mall.mapper.FrontCategoryToCategoryMapper;
import net.summerfarm.mall.mapper.MajorPriceMapper;
import net.summerfarm.mall.model.domain.Category;
import net.summerfarm.mall.model.domain.FrontCategory;
import net.summerfarm.mall.model.vo.FrontCategoryVO;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.service.*;
import net.xianmu.common.cache.InMemoryCache;
import net.xianmu.common.result.CommonResult;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-05-29
 * @description
 */
@Service
public class FrontCategoryServiceImpl implements FrontCategoryService {
    @Resource
    private FrontCategoryMapper frontCategoryMapper;
    @Resource
    private FrontCategoryToCategoryMapper frontCategoryToCategoryMapper;
    @Resource
    private MajorPriceMapper majorPriceMapper;
    @Resource
    private CategoryMapper categoryMapper;
    @Resource
    private InventoryService inventoryService;
    @Resource
    private TimingDeliveryService timingDeliveryService;
    @Resource
    private CategoryService categoryService;


    @Override
    public CommonResult<List<FrontCategoryVO>> selectFrontCategory() {
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        if (merchantSubject == null) {
            merchantSubject = new MerchantSubject();
        }

        List<String> skuList = new ArrayList<>();
        if (RequestHolder.isMajor()) {
            skuList = majorPriceMapper.selectSKu(merchantSubject.getAdminId(), merchantSubject.getArea().getAreaNo(), merchantSubject.getDirect(), MajorPriceMallShowEnum.YES.ordinal());
            //获取类目sku信息
            List<String> majorSkuList = inventoryService.getBigMerchantMajorCategory(merchantSubject.getAdminId(), merchantSubject.getArea().getAreaNo(), merchantSubject.getDirect(), MajorPriceMallShowEnum.YES.ordinal());
            skuList.addAll(majorSkuList);
        }

        //小程序过滤部分类目
        Set<Integer> unShowCate = null;
        if (RequestHolder.isMiniProgramLogin()) {
            unShowCate = categoryService.getUnShowCategoryId();
        }
        List<FrontCategoryVO> categoryCList;
        List<Category> categoryList = categoryMapper.selectCategorys(merchantSubject.getArea().getAreaNo(), merchantSubject.getAdminId(), skuList, merchantSubject.getSkuShow(), merchantSubject.getDirect(), merchantSubject.getSize(), unShowCate);
        if(!CollectionUtils.isEmpty(categoryList)){
            List<Integer> categoryIds = categoryList.stream().filter(Objects::nonNull).map(Category::getId).collect(Collectors.toList());
            categoryCList = selectFrontByCategoryRelateType(categoryIds);
        }else{
            categoryCList = Collections.emptyList ();
        }

        List<FrontCategoryVO> skuCList = Collections.emptyList ();
        if(merchantSubject.getArea() != null){
            skuCList = selectFrontBySkuRelateType(RequestHolder.isMajor(),merchantSubject.getArea().getAreaNo());
        }

        List<FrontCategoryVO> all = new ArrayList<> (Stream.concat (
                        (skuCList != null ? skuCList.stream () : Stream.empty ()),
                        (categoryCList != null ? categoryCList.stream () : Stream.empty ())
                )
                .filter (vo -> vo.getId () != null) // 确保 id 不为空
                .collect (Collectors.toMap (
                        FrontCategoryVO::getId, // 使用 id 作为键
                        vo -> vo, // 使用 vo 作为值
                        (existing, replacement) -> existing)) // 如果出现重复，保留现有的值
                .values ());


        return CommonResult.ok(buildFrontCategory(all));
    }
    private List<FrontCategoryVO> selectFrontBySkuRelateType(boolean major, Integer areaNo) {
        if(major){
            return Collections.emptyList ();
        }
        //查询可见二级类目
        return frontCategoryMapper.selectBySkuRelateType(areaNo);
    }
    private List<FrontCategoryVO> selectFrontByCategoryRelateType(List<Integer> categoryIds) {
        if(CollectionUtil.isEmpty (categoryIds)){
            return Collections.emptyList ();
        }
        return frontCategoryMapper.selectByCategoryIds(categoryIds, RequestHolder.getMerchantAreaNo());
    }

    @Override
    public CommonResult<List<FrontCategoryVO>> selectFrontByCategoryIds(List<Integer> categoryIds) {
        return CommonResult.ok(buildFrontCategory(selectFrontByCategoryRelateType(categoryIds)));
    }

    private List<FrontCategoryVO> buildFrontCategory(List<FrontCategoryVO> seconds){
        if(CollectionUtil.isEmpty (seconds)){
            return Collections.emptyList ();
        }
        //查询全部一级类目
        List<FrontCategoryVO> parentList = frontCategoryMapper.selectParentFrontCategory(RequestHolder.getMerchantAreaNo());

        //组装数据
        List<FrontCategoryVO> result = new ArrayList<>();
        Map<Integer, List<FrontCategoryVO>> voMap = seconds.stream().collect(Collectors.groupingBy(FrontCategory::getParentId));
        for (FrontCategoryVO parent : parentList) {
            if(Objects.equals(0, parent.getDisplay())){
                continue;
            }
            if(voMap.containsKey(parent.getId())){
                List<FrontCategoryVO> children = voMap.get(parent.getId());
                // 排序
                sortFrontCategoryVO(children);

                parent.setChildList(children);
                result.add(parent);
            }
        }
        return result;
    }

    private void sortFrontCategoryVO(List<FrontCategoryVO> children) {
        // 排序
        children.sort ((o1, o2) -> {
            // 处理 priority 可能为 null 的情况
            Integer priority1 = o1.getPriority ();
            Integer priority2 = o2.getPriority ();

            // 先比较 priority，null 值视为优先级最低
            if (priority1 == null && priority2 == null) {
                return 0; // 两者都为 null，视为相等
            } else if (priority1 == null) {
                return 1; // o1 的 priority 为 null，排在后面
            } else if (priority2 == null) {
                return -1; // o2 的 priority 为 null，排在后面
            }

            // 当 priority 都不为 null 时，按 priority 降序排序
            int priorityCompare = priority2.compareTo (priority1);
            if (priorityCompare != 0) {
                return priorityCompare;
            }

            // 如果 priority 相同，则按照 id 降序排序
            return o2.getId ().compareTo (o1.getId ());
        });
    }

    @Override
    public CommonResult<List<FrontCategoryVO>> listTimingFrontCategory() {
        CommonResult<List<Integer>> categoryResult = timingDeliveryService.listTimingCategory(
                RequestHolder.getMerchantAreaNo());
        List<Integer> categoryIds = categoryResult.getData();
        if (CollectionUtils.isEmpty(categoryIds)) {
            return CommonResult.ok(Lists.newArrayList());
        }
        return selectFrontByCategoryIds(categoryIds);
    }
    @Override
    @InMemoryCache(expiryTimeInSeconds = 3 * 60)
    public List<Integer> selectCategoryId(Integer frontCategoryId) {
        return frontCategoryToCategoryMapper.selectCategoryId(frontCategoryId);
    }
}
