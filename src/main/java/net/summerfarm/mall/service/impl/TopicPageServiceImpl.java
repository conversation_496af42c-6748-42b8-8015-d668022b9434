package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum;
import net.summerfarm.mall.common.util.PageInfoHelper;
import net.summerfarm.mall.common.util.RedisCacheUtil;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.CouponReceiveStatusEnum;
import net.summerfarm.mall.enums.market.ReceiveCouponErrorEnum;
import net.summerfarm.mall.enums.market.ScopeTypeEnum;
import net.summerfarm.mall.mapper.MerchantPoolDetailMapper;
import net.summerfarm.mall.mapper.MerchantPoolInfoMapper;
import net.summerfarm.mall.model.bo.coupon.CouponSenderBO;
import net.summerfarm.mall.model.domain.Coupon;
import net.summerfarm.mall.model.domain.MerchantPoolDetail;
import net.summerfarm.mall.model.domain.ProductsPropertyValue;
import net.summerfarm.mall.model.dto.market.coupon.CouponDTO;
import net.summerfarm.mall.model.dto.market.topicpage.*;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.facade.CmsQueryFacade;
import net.summerfarm.mall.service.helper.ExchangeBuyServiceHelper;
import net.summerfarm.mall.service.strategy.coupon.CouponSenderContext;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.marketing.center.client.cms.common.enums.ComponentEnum;
import net.xianmu.marketing.center.client.cms.req.ContentPageReq;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @create: 2023/7/24
 */
@Slf4j
@Service
public class TopicPageServiceImpl implements TopicPageService {

    @Resource
    private CmsQueryFacade cmsQueryFacade;

    @Resource
    private SalePriceTransService salePriceTransService;

    @Resource
    private CouponSenderService couponSenderService;

    @Resource
    private InventoryService inventoryService;

    @Resource
    private RedisCacheUtil redisCacheUtil;

    @Resource
    private CouponSenderContext couponSenderContext;

    @Resource
    private ConfigService configService;

    @Resource
    private MerchantPoolService merchantPoolService;

    private static final Integer MAX_COUPON_NUM = 20;

    @Override
    public CommonResult<TopicPageDTO> showFrame(Long topicPageId) {
        //20240725 飞书沟通POP大客户放开城市维度的限制其他大客户动态配置是否开启（默认不开启） -- by薄荷
        String bollen = configService.getValue(Global.MAJOR_MARKETING_RESTRICT);
        if (RequestHolder.isMajor() && !RequestHolder.isPopMerchant() && !Boolean.parseBoolean(bollen)) {//NOSONAR
            return CommonResult.ok();
        }
        TopicPageDTO topicPageDTO = cmsQueryFacade.listAllLaunchById(topicPageId);
        if (topicPageDTO == null || CollectionUtil.isEmpty(topicPageDTO.getTopicComponentList())) {
            log.warn("专题页没有组件,topicPageId:{}", topicPageId);
            return CommonResult.ok(topicPageDTO);
        }
        List<TopicComponentDTO> componentList = topicPageDTO.getTopicComponentList();
        List<TopicComponentLaunchDTO> launchList = Lists.newArrayList();
        //默认全是人群包配置
        for (TopicComponentDTO componentDTO : componentList) {
            launchList.addAll(componentDTO.getComponentLaunchList());
        }

        Map<Long, List<Long>> scopeLaunchMap = launchList.stream()
                .filter(x -> Objects.equals(x.getScopeType(),
                        ScopeTypeEnum.MERCHANT_POOL.getCode()) || Objects.equals(x.getScopeType(),
                        ScopeTypeEnum.ALL.getCode()))
                .collect(Collectors.toMap(x -> x.getScopeId(),
                        x -> Lists.newArrayList(x.getId()), (a, b) -> {
                            a.addAll(b);
                            return a;
                        }));
        List<Long> poolInfoIds = Lists.newArrayList(scopeLaunchMap.keySet());
        if (CollectionUtil.isEmpty(poolInfoIds)) {
            return CommonResult.ok(topicPageDTO);
        }

        //过滤人群包id,取人群包最新版本的详情
        Long mId = RequestHolder.getMId();
        List<MerchantPoolDetail> merchantPoolDetailList = merchantPoolService.getDetailByMIdWithCache(mId);

        //所有满足的投放id,全部人群
        List<Long> filterLaunchIds = Lists.newArrayList();
        if (scopeLaunchMap.get(0L) != null) {
            filterLaunchIds.addAll(scopeLaunchMap.get(0L));
        }

        if (CollectionUtil.isNotEmpty(merchantPoolDetailList)) {
            //过滤出符合的人群包id
            List<Long> filerPoolInfoIds = merchantPoolDetailList.stream().map(MerchantPoolDetail::getPoolInfoId)
                    .collect(Collectors.toList());
            for (Long poolInfoId : filerPoolInfoIds) {
                List<Long> launchIds = scopeLaunchMap.get(poolInfoId);
                if (CollectionUtil.isNotEmpty(launchIds)) {
                    filterLaunchIds.addAll(launchIds);
                }
            }
        }
        if (CollectionUtil.isEmpty(filterLaunchIds)) {
            log.warn("获取人群包为空,topicPageId:{},mId:{}", topicPageId, mId);
            topicPageDTO.setTopicComponentList(null);
            return CommonResult.ok(topicPageDTO);
        }

        //匹配对应的投放和组件,一个组件多个投放的时候优先取投放1的
        Map<Long, TopicComponentLaunchDTO> componentMap = launchList.stream()
                .filter(x -> filterLaunchIds.contains(x.getId()))
                .collect(Collectors.toMap(x -> x.getComponentInfoId(), Function.identity(),
                        BinaryOperator.minBy(
                                Comparator.comparing(TopicComponentLaunchDTO::getPriority))));

        //匹配命中的组件
        List<TopicComponentDTO> filterComponents = componentList.stream()
                .filter(x -> componentMap.keySet().contains(x.getId()))
                .sorted(Comparator.comparing(TopicComponentDTO::getPriority)).collect(
                        Collectors.toList());

        //tab组件还需要获取tab标签以及子组件
        for (TopicComponentDTO component : filterComponents) {
            TopicComponentLaunchDTO launchDTO = componentMap.get(component.getId());
            component.setComponentLaunch(launchDTO);
            //图片组件、容器组件需要继续匹配
            if (Objects.equals(component.getComponentType(), 1) || Objects.equals(
                    component.getType(), 0)) {
                //获取投放
                Long launchId = launchDTO.getId();
                ContentPageReq pageReq = new ContentPageReq();
                pageReq.setPageNo(1);
                pageReq.setPageSize(100);
                pageReq.setLaunchId(launchId);
                //一个专题页应该不会有太多的tab组件，所以遍历处理实际只会调用几次
                TopicComponentLaunchDTO componentLaunchDTO = cmsQueryFacade.pageContentByQuery(
                        pageReq);
                if (componentLaunchDTO == null) {
                    log.warn("获取投放内容失败,topicPageId:{},launchId:{}", topicPageId, launchId);
                    continue;
                }
                launchDTO.setComponentDetailList(componentLaunchDTO.getComponentDetailList());
            }
            if (Objects.equals(component.getComponentType(), 1)) {
                for (TopicComponentDetailDTO detailDTO : launchDTO.getComponentDetailList()) {
                    for (TopicComponentDTO componentDTO : detailDTO.getChildrenComponentList()) {
                        componentDTO.setComponentLaunchList(null);
                    }
                }
            }
            component.setComponentLaunchList(null);
        }
        topicPageDTO.setTopicComponentList(filterComponents);
        return CommonResult.ok(topicPageDTO);
    }

    @Override
    public CommonResult<PageInfo<ProductInfoVO>> listTopicSku(TopicLaunchReq launchReq) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        ContentPageReq pageReq = new ContentPageReq();
        pageReq.setPageNo(launchReq.getPageNo());
        pageReq.setPageSize(launchReq.getPageSize());
        pageReq.setLaunchId(launchReq.getLaunchId());
        // 判断是否商品组件
        TopicComponentLaunchDTO componentLaunchDTO = redisCacheUtil.getDataWithCache(
                launchReq.getLaunchId() + "", 60, TopicComponentLaunchDTO.class,
                () -> cmsQueryFacade.pageContentByQuery(pageReq));
        if (componentLaunchDTO == null || CollectionUtil.isEmpty(
                componentLaunchDTO.getComponentDetailList())) {
            log.warn("获取商品，投放内容失败,launchReq:{}", launchReq);
            return CommonResult.ok(PageInfo.emptyPageInfo());
        }
        List<String> skus = componentLaunchDTO.getComponentDetailList().stream()
                .map(x -> x.getBizInfo()).distinct()
                .collect(Collectors.toList());
        List<ProductInfoVO> productInfoList = salePriceTransService.selectRecommendProductVO(
                RequestHolder.getMerchantAreaNo(), skus);
        if (CollectionUtil.isEmpty(productInfoList)) {
            log.warn("获取商品详情列表为空,launchReq:{}", launchReq);
            return CommonResult.ok(PageInfo.emptyPageInfo());
        }
        //filterType包含1的话需要过滤无库存的
        if (componentLaunchDTO.getFilterType().contains("1")) {
            productInfoList = productInfoList.stream().filter(x -> x.getQuantity() > 0)
                    .collect(Collectors.toList());
        }
        //需要重新排序
        productInfoList.sort(
                Comparator.comparing(ProductInfoVO::getSku, Comparator.comparing(skus::indexOf)));

        PageInfo<ProductInfoVO> pageInfo = PageInfoHelper.manualPage(
                launchReq.getPageNo(), launchReq.getPageSize(), productInfoList);
        //营销活动处理 并且设置均价信息
        inventoryService.checkoutSku(pageInfo.getList(), RequestHolder.getMerchantSubject());
        log.info("耗时{}ms", stopWatch.getTime());
        return CommonResult.ok(pageInfo);
    }

    @Override
    public CommonResult<PageInfo<Coupon>> listTopicCoupon(TopicLaunchReq launchReq) {
        ContentPageReq pageReq = new ContentPageReq();
        pageReq.setPageNo(launchReq.getPageNo());
        pageReq.setPageSize(launchReq.getPageSize());
        pageReq.setLaunchId(launchReq.getLaunchId());
        //判断是否优惠券组件
        TopicComponentLaunchDTO componentLaunchDTO = cmsQueryFacade.pageContentByQuery(pageReq);
        if (componentLaunchDTO == null || CollectionUtil.isEmpty(
                componentLaunchDTO.getComponentDetailList())) {
            log.warn("获取优惠券投放内容失败,launchReq:{}", launchReq);
            return CommonResult.ok(PageInfo.emptyPageInfo());
        }
        List<Coupon> coupons = listAllCoupons(componentLaunchDTO);
        PageInfo<Coupon> pageInfo = PageInfoHelper.manualPage(
                launchReq.getPageNo(), launchReq.getPageSize(), coupons);
        return CommonResult.ok(pageInfo);
    }

    private List<Coupon> listAllCoupons(TopicComponentLaunchDTO componentLaunchDTO) {
        List<Integer> couponIds = componentLaunchDTO.getComponentDetailList().stream()
                .map(x -> Integer.valueOf(x.getBizInfo()))
                .collect(Collectors.toList());
        CouponDTO couponDTO = new CouponDTO();
        couponDTO.setCouponId(couponIds);
        couponDTO.setMId(RequestHolder.getMId());
        List<Coupon> coupons = couponSenderService.getBatchCouponInfo(couponDTO);
        if (CollectionUtil.isEmpty(coupons)) {
            return Lists.newArrayList();
        }
        //filterType包含1的话需要过滤已抢光的
        if (componentLaunchDTO.getFilterType().contains("1")) {
            coupons = coupons.stream().filter(x -> !Objects.equals(x.getReceiveStatus(),
                            CouponReceiveStatusEnum.SOLD_OUT.getCode()))
                    .collect(Collectors.toList());
        }
        if (CollectionUtil.isEmpty(coupons)) {
            return Lists.newArrayList();
        }
        coupons.sort(Comparator.comparing(Coupon::getId, Comparator.comparing(couponIds::indexOf)));
        return coupons;
    }

    @Override
    public CommonResult<Integer> oneClickReceiveCoupon(Long launchId) {
        ContentPageReq pageReq = new ContentPageReq();
        pageReq.setPageNo(1);
        pageReq.setPageSize(MAX_COUPON_NUM);
        pageReq.setLaunchId(launchId);
        //判断是否优惠券组件
        TopicComponentLaunchDTO componentLaunchDTO = cmsQueryFacade.pageContentByQuery(pageReq);
        if (componentLaunchDTO == null || CollectionUtil.isEmpty(
                componentLaunchDTO.getComponentDetailList()) || !Objects.equals(
                componentLaunchDTO.getType(),
                ComponentEnum.COUPON.getCode())) {
            log.warn("一键领券失败,launchId:{}", launchId);
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "页面已失效，请重新刷新！");
        }
        //优惠券组件styleType=3 是图片组件
        if (!Objects.equals(componentLaunchDTO.getStyleType(), 3)) {
            log.warn("一键领券失败,launchId:{}", launchId);
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "当前页面不支持一键领券！");
        }
        List<Coupon> coupons = listAllCoupons(componentLaunchDTO);
        if (CollectionUtil.isEmpty(coupons)) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, ReceiveCouponErrorEnum.INVALID.getValue());
        }
        List<CouponSenderBO> list = Lists.newArrayList();
        List<Integer> successCouponIds = Lists.newArrayList();
        for (Coupon coupon : coupons) {
            CouponSenderBO couponSenderBO = new CouponSenderBO();
            try {
                couponSenderBO.setCouponId(coupon.getId());
                couponSenderBO.setMId(RequestHolder.getMId());
                couponSenderBO.setSenderType(CouponSenderSetupSenderTypeEnum.USER_RECEIVE);
                couponSenderContext.receiveNew(couponSenderBO);
                successCouponIds.add(coupon.getCouponId());
            } catch (BizException e) {
                list.add(couponSenderBO);
                log.warn("一键领券失败,launchId:{},couponId:{},cause:{}", launchId, coupon.getId(),
                        e.getMessage());
            } catch (Exception e) {
                log.warn("一键领券失败,launchId:{},couponId:{},cause:{}", launchId, coupon.getId(),
                        Throwables.getStackTraceAsString(e));
            }
        }
        //全部领取失败
        if (list.size() == coupons.size()) {
            list.sort(Comparator.comparing(CouponSenderBO::getErrorEnum, Comparator.comparing(
                    ReceiveCouponErrorEnum.SORT_INDEX::indexOf)));
            ReceiveCouponErrorEnum errorEnum = list.get(0).getErrorEnum();
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, errorEnum.getValue());
        }
        log.info("一键领券成功,launchId:{},couponIds:{}", launchId, successCouponIds);
        //领取成功数量
        return CommonResult.ok(coupons.size() - list.size());
    }

    @Override
    public List<ProductInfoVO> lowLevelSkus(TopicLowLevelSkuReq request) {
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();

            //根据专题页ID获取专题页对应投放明细内容--第一次查询后加入缓存默认10min失效
            TopicPageDTO topicPageDTO = cmsQueryFacade.queryPageDetailById(request.getTopicId());
            if (topicPageDTO == null) {
                log.info("TopicPageService[]lowLevelSkus[]topicPageDTO is null,topicId:{}", request.getTopicId());
                return Collections.emptyList();
            }
            List<TopicComponentDTO> componentList = topicPageDTO.getTopicComponentList();

            //过滤图片以及优惠券组件 只保留商品和tab组件
            componentList = componentList.stream().filter(componentDTO ->
                    Objects.equals(componentDTO.getType(), ComponentEnum.ITEM.getCode()) ||
                            Objects.equals(componentDTO.getType(), ComponentEnum.TAB.getCode())
            ).collect(Collectors.toList());

            //默认全是人群包配置
            List<TopicComponentLaunchDTO> launchList = Lists.newArrayList();
            for (TopicComponentDTO componentDTO : componentList) {
                launchList.addAll(componentDTO.getComponentLaunchList());
            }
            Map<Long, List<Long>> scopeLaunchMap = launchList.stream()
                    .filter(x -> Objects.equals(x.getScopeType(),
                            ScopeTypeEnum.MERCHANT_POOL.getCode()) || Objects.equals(x.getScopeType(),
                            ScopeTypeEnum.ALL.getCode()))
                    .collect(Collectors.toMap(x -> x.getScopeId(),
                            x -> Lists.newArrayList(x.getId()), (a, b) -> {
                                a.addAll(b);
                                return a;
                            }));
            List<Long> poolInfoIds = Lists.newArrayList(scopeLaunchMap.keySet());
            if (CollectionUtil.isEmpty(poolInfoIds)) {
                log.info("TopicPageService[]lowLevelSkus[]poolInfoIds is empty,topicId:{}", request.getTopicId());
                return Collections.emptyList();
            }

            //过滤人群包id,取人群包最新版本的详情
            Long mId = RequestHolder.getMId();
            List<MerchantPoolDetail> detailByMIdWithCache = merchantPoolService.getDetailByMIdWithCache(mId);

            //所有满足的投放id,全部人群
            List<Long> filterLaunchIds = Lists.newArrayList();
            if (scopeLaunchMap.get(0L) != null) {
                filterLaunchIds.addAll(scopeLaunchMap.get(0L));
            }

            if (CollectionUtil.isNotEmpty(detailByMIdWithCache)) {
                //过滤出符合的人群包id
                List<Long> filerPoolInfoIds = detailByMIdWithCache.stream().map(MerchantPoolDetail::getPoolInfoId)
                        .collect(Collectors.toList());;
                for (Long poolInfoId : filerPoolInfoIds) {
                    List<Long> launchIds = scopeLaunchMap.get(poolInfoId);
                    if (CollectionUtil.isNotEmpty(launchIds)) {
                        filterLaunchIds.addAll(launchIds);
                    }
                }
            }
            if (CollectionUtil.isEmpty(filterLaunchIds)) {
                log.info("TopicPageService[]lowLevelSkus[]filterLaunchIds is empty,topicId:{}", request.getTopicId());
                return Collections.emptyList();
            }

            //匹配对应的投放和组件,一个组件多个投放的时候优先取投放1的
            Map<Long, TopicComponentLaunchDTO> componentMap = launchList.stream()
                    .filter(x -> filterLaunchIds.contains(x.getId()))
                    .collect(Collectors.toMap(x -> x.getComponentInfoId(), Function.identity(),
                            BinaryOperator.minBy(
                                    Comparator.comparing(TopicComponentLaunchDTO::getPriority))));

            //匹配命中的组件
            List<TopicComponentDTO> filterComponents = componentList.stream()
                    .filter(x -> componentMap.keySet().contains(x.getId()))
                    .sorted(Comparator.comparing(TopicComponentDTO::getPriority)).collect(
                            Collectors.toList());

            //匹配需要无库存过滤的sku
            Set<String> filterSkus = new HashSet<>();

            //遍历组件获取商品信息（假如tab组件只获取下面的商品组件）
            List<String> skus = new ArrayList<>();
            for (TopicComponentDTO filterComponent : filterComponents) {
                //只取第一个投放
                TopicComponentLaunchDTO launchDTO = componentMap.get(filterComponent.getId());
                if (Objects.equals(filterComponent.getType(), ComponentEnum.TAB.getCode())) {
                    for (TopicComponentDetailDTO topicComponentDetailDTO : launchDTO.getComponentDetailList()) {
                        for (TopicComponentDTO topicComponentDTO : topicComponentDetailDTO.getChildrenComponentList()) {
                            if (!Objects.equals(topicComponentDTO.getType(), ComponentEnum.ITEM.getCode())) {
                                continue;
                            }
                            for (TopicComponentLaunchDTO componentLaunchDTO : topicComponentDTO.getComponentLaunchList()) {
                                List<String> collect = componentLaunchDTO.getComponentDetailList().stream().map(TopicComponentDetailDTO::getBizInfo)
                                        .distinct().collect(Collectors.toList());
                                if (componentLaunchDTO.getFilterType().contains("1")) {
                                    filterSkus.addAll(collect);
                                }
                                skus.addAll(collect);
                            }
                        }
                    }
                } else if (Objects.equals(filterComponent.getType(), ComponentEnum.ITEM.getCode())) {
                    List<String> skuCollect = launchDTO.getComponentDetailList().stream().map(TopicComponentDetailDTO::getBizInfo)
                            .distinct().collect(Collectors.toList());
                    if (launchDTO.getFilterType().contains("1")) {
                        filterSkus.addAll(skuCollect);
                    }
                    skus.addAll(skuCollect);
                }
            }

            if (CollectionUtils.isEmpty(skus)) {
                log.info("TopicPageService[]lowLevelSkus[]skus is empty,topicId:{}", request.getTopicId());
                return Collections.emptyList();
            }

            //查询sku信息包含库存
            List<ProductInfoVO> productInfoList = salePriceTransService.selectRecommendProductVO(
                    RequestHolder.getMerchantAreaNo(), skus);
            if (CollectionUtils.isEmpty(productInfoList)) {
                log.info("TopicPageService[]lowLevelSkus[]productInfoList is empty,topicId:{}", request.getTopicId());
                return Collections.emptyList();
            }

            //设置了无库存不展示 需要过滤无库存的sku
            if (!CollectionUtil.isEmpty(filterSkus)) {
                Iterator<ProductInfoVO> iterator = productInfoList.iterator();
                while (iterator.hasNext()) {
                    ProductInfoVO next = iterator.next();
                    if (filterSkus.contains(next.getSku()) && next.getQuantity() <= 0) {
                        iterator.remove();
                    }
                }
            }

            //需要重新排序
            productInfoList.sort(
                    Comparator.comparing(ProductInfoVO::getSku, Comparator.comparing(skus::indexOf)));

            //假如productInfoList大于四个则截取前四个 否则剩下的都返回
            if (productInfoList.size() > 4) {
                productInfoList = productInfoList.subList(0, 4);
            }

            //营销活动处理
            inventoryService.checkoutSku(productInfoList, RequestHolder.getMerchantSubject());

            //隐藏库存信息
            productInfoList.forEach(productInfoVO -> {
                productInfoVO.setQuantity(10000);
                productInfoVO.setOnlineQuantity(10000);
            });
            log.info("TopicPageService[]lowLevelSkus[]耗时{}ms", stopWatch.getTime());
            return productInfoList;
        } catch (Exception e) {
            log.warn("TopicPageService[]lowLevelSkus[]error[]topicId:{}, cause:{}", request.getTopicId(), Throwables.getStackTraceAsString(e));
            return Collections.emptyList();
        }
    }
}
