package net.summerfarm.mall.service.search;

import java.util.Comparator;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.facade.engine.model.EsMarketItemInfoDTO;
import net.summerfarm.mall.service.search.function.SkuCategoryFunction;
import net.summerfarm.mall.service.search.function.SkuSalesVolumeFunction;
import net.summerfarm.mall.service.search.function.SkuStockFunction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 商品搜索批量分散重排序服务 实现Python batch_disperse_skus功能的Java版本
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Slf4j
@Service
public class ProductSearchBatchDisperseService {

    @Autowired
    private SkuSalesVolumeCacheService skuSalesVolumeCacheService;

    /**
     * 分段重排序SKU列表，将每段内无库存的SKU排到段末，并可选按销量倒序排序。 支持在销量排序时，按类目分组后组内倒序排序，保持不同类目SKU的原有顺序。
     *
     * @param skuList             待处理的SKU列表
     * @param stockFunction       判断SKU是否有库存的函数，返回true表示有库存
     * @param batchSize           每段SKU数量
     * @param salesVolumeFunction 获取SKU销量的函数，返回销量数值
     * @param categoryFunction    获取SKU类目的函数，返回类目标识
     * @return 处理后的SKU列表
     */
    public List<EsMarketItemInfoDTO> batchDisperseSkus(
        List<EsMarketItemInfoDTO> skuList,
        SkuStockFunction stockFunction,
        int batchSize,
        SkuSalesVolumeFunction salesVolumeFunction,
        SkuCategoryFunction categoryFunction) {

        if (CollectionUtils.isEmpty(skuList) || batchSize <= 0) {
            return skuList;
        }

        log.info("开始批量分散重排序，SKU总数: {}, 批次大小: {}", skuList.size(), batchSize);

        // 如果提供了销量函数，预先批量获取所有SKU的销量数据，减少Redis网络请求
        Map<String, BigDecimal> salesVolumeCache = null;
        if (salesVolumeFunction != null) {
            long salesStartTime = System.currentTimeMillis();
            Set<String> allSkuCodes = skuList.stream()
                .map(EsMarketItemInfoDTO::getItemCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

            salesVolumeCache = skuSalesVolumeCacheService.getSkuSalesVolumeBatch(allSkuCodes);
            log.info("预先批量获取销量数据完成，SKU数量: {}, 耗时: {}ms",
                allSkuCodes.size(), System.currentTimeMillis() - salesStartTime);
        }

        List<EsMarketItemInfoDTO> result = new ArrayList<>();

        for (int i = 0; i < skuList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, skuList.size());
            List<EsMarketItemInfoDTO> batch = new ArrayList<>(skuList.subList(i, endIndex));

            try {
                // 如果提供了库存函数，则优先按库存状态分组
                if (stockFunction != null) {
                    log.debug("先按库存排序，有库存的在前");
                    List<EsMarketItemInfoDTO> inStock = batch.stream()
                        .filter(stockFunction::apply)
                        .collect(Collectors.toList());
                    List<EsMarketItemInfoDTO> outOfStock = batch.stream()
                        .filter(sku -> !stockFunction.apply(sku))
                        .collect(Collectors.toList());

                    // 对有库存和无库存的列表分别应用后续的排序逻辑
                    batch = new ArrayList<>();
                    batch.addAll(sortSubList(inStock, salesVolumeCache, categoryFunction));
                    batch.addAll(sortSubList(outOfStock, salesVolumeCache, categoryFunction));
                } else {
                    // 如果没有库存函数，则对整个批次应用排序逻辑
                    batch = sortSubList(batch, salesVolumeCache, categoryFunction);
                }

            } catch (Exception e) {
                // 如果排序过程中出现异常，记录错误但不影响整体流程
                log.error("排序过程中出现异常: {}, 跳过当前批次排序", e.getMessage(), e);
            }

            result.addAll(batch);
        }

        log.info("批量分散重排序完成，处理后SKU总数: {}", result.size());
        return result;
    }

    /**
     * 根据销量和类目对子列表进行排序的辅助方法
     *
     * @param subList          待排序的SKU子列表
     * @param salesVolumeCache 预先获取的销量缓存数据
     * @param categoryFunction 获取SKU类目的函数
     * @return 排序后的SKU列表
     */
    private List<EsMarketItemInfoDTO> sortSubList(
        List<EsMarketItemInfoDTO> subList,
        Map<String, BigDecimal> salesVolumeCache,
        SkuCategoryFunction categoryFunction) {

        if (CollectionUtils.isEmpty(subList)) {
            return subList;
        }

        // 如果同时提供了类目和销量缓存，则按类目分组，组内按销量排序
        if (categoryFunction != null && salesVolumeCache != null) {
            log.info("按类目分组，组内按销量倒序排序");
            return sortByCategoryAndSales(subList, categoryFunction, salesVolumeCache);
        }
        // 如果只提供了销量缓存，则仅按销量倒序排序
        else if (salesVolumeCache != null) {
            log.info("仅按销量倒序排序");
            subList.sort((sku1, sku2) -> {
                BigDecimal sales1 = salesVolumeCache.getOrDefault(sku1.getItemCode(), BigDecimal.ZERO);
                BigDecimal sales2 = salesVolumeCache.getOrDefault(sku2.getItemCode(), BigDecimal.ZERO);
                return sales2.compareTo(sales1); // 倒序排序
            });
            return subList;
        }
        // 其他情况（例如只有类目函数，或都没有提供）不改变顺序
        return subList;
    }

    /**
     * 按类目分组并在组内按销量倒序排序，保持类目间的原有顺序。
     * <p>
     * 该方法会将输入的SKU列表按照类目进行分组，每组内部根据销量进行降序排序， 同时保持不同类目之间的原始顺序（即第一个出现的类目排在最前面）。
     *
     * @param batch            待排序的SKU批次，不可为null，通常不超过20个元素
     * @param categoryFunction 用于从SKU中提取类目ID的函数，不可为null
     * @param salesVolumeCache 包含SKU销量数据的缓存Map，key为SKU编码，value为销量，不可为null
     * @return 排序后的SKU列表，保持类目间原始顺序，类目内按销量降序排列
     * @throws NullPointerException     如果任何参数为null
     * @throws IllegalArgumentException 如果batch中包含null元素
     */
    private List<EsMarketItemInfoDTO> sortByCategoryAndSales(
        List<EsMarketItemInfoDTO> batch,
        SkuCategoryFunction categoryFunction,
        Map<String, BigDecimal> salesVolumeCache) {

        if (CollectionUtils.isEmpty(batch) || categoryFunction == null || salesVolumeCache == null) {
            log.error("参数batch、categoryFunction或salesVolumeCache不能为空, 不允许排序");
            return batch;
        }

        if (batch.stream().anyMatch(Objects::isNull)) {
            log.error("batch中不能包含null元素, 不允许排序");
            return batch;
        }

        List<EsMarketItemInfoDTO> subBatchToSort = new ArrayList<>();
        List<EsMarketItemInfoDTO> finalBatch = new ArrayList<>(batch.size());
        Long currentCategory = categoryFunction.apply(batch.get(0));
        subBatchToSort.add(batch.get(0));

        final Comparator<EsMarketItemInfoDTO> salesComparator = (sku1, sku2) -> {
            BigDecimal sales1 = salesVolumeCache.getOrDefault(sku1.getItemCode(), BigDecimal.ZERO);
            BigDecimal sales2 = salesVolumeCache.getOrDefault(sku2.getItemCode(), BigDecimal.ZERO);
            return sales2.compareTo(sales1); // 倒序排序
        };

        for (int index = 1, batchSize = batch.size(); index < batchSize; index++) {
            EsMarketItemInfoDTO sku = batch.get(index);
            Long category = categoryFunction.apply(sku);
            if (currentCategory.equals(category)) {
                subBatchToSort.add(sku);
            } else {
                log.info("当前类目: {}, 下一个类目:{}", currentCategory, category);
                subBatchToSort.sort(salesComparator);
                finalBatch.addAll(subBatchToSort); // 将排序后的子批次添加到最终批次

                subBatchToSort = new ArrayList<>();
                currentCategory = category;
                subBatchToSort.add(sku);
            }
        }

        if (!CollectionUtils.isEmpty(subBatchToSort)) {
            // 处理最后一个子批次
            subBatchToSort.sort(salesComparator);
            finalBatch.addAll(subBatchToSort);
        }

        return finalBatch;
    }

    /**
     * 分段重排序SKU列表的向后兼容版本（保持原有接口不变）
     *
     * @param skuList             待处理的SKU列表
     * @param stockFunction       判断SKU是否有库存的函数，返回true表示有库存
     * @param batchSize           每段SKU数量
     * @param salesVolumeFunction 获取SKU销量的函数，返回销量数值
     * @param categoryFunction    获取SKU类目的函数，返回类目标识
     * @return 处理后的SKU列表
     */
    public List<EsMarketItemInfoDTO> batchDisperseSkusLegacy(
        List<EsMarketItemInfoDTO> skuList,
        SkuStockFunction stockFunction,
        int batchSize,
        SkuSalesVolumeFunction salesVolumeFunction,
        SkuCategoryFunction categoryFunction) {

        if (CollectionUtils.isEmpty(skuList) || batchSize <= 0) {
            return skuList;
        }

        log.info("开始批量分散重排序（兼容模式），SKU总数: {}, 批次大小: {}", skuList.size(), batchSize);

        List<EsMarketItemInfoDTO> result = new ArrayList<>();

        for (int i = 0; i < skuList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, skuList.size());
            List<EsMarketItemInfoDTO> batch = new ArrayList<>(skuList.subList(i, endIndex));

            try {
                // 如果提供了库存函数，则优先按库存状态分组
                if (stockFunction != null) {
                    log.debug("先按库存排序，有库存的在前");
                    List<EsMarketItemInfoDTO> inStock = batch.stream()
                        .filter(stockFunction::apply)
                        .collect(Collectors.toList());
                    List<EsMarketItemInfoDTO> outOfStock = batch.stream()
                        .filter(sku -> !stockFunction.apply(sku))
                        .collect(Collectors.toList());

                    // 对有库存和无库存的列表分别应用后续的排序逻辑
                    batch = new ArrayList<>();
                    batch.addAll(sortSubListLegacy(inStock, salesVolumeFunction, categoryFunction));
                    batch.addAll(sortSubListLegacy(outOfStock, salesVolumeFunction, categoryFunction));
                } else {
                    // 如果没有库存函数，则对整个批次应用排序逻辑
                    batch = sortSubListLegacy(batch, salesVolumeFunction, categoryFunction);
                }

            } catch (Exception e) {
                // 如果排序过程中出现异常，记录错误但不影响整体流程
                log.error("排序过程中出现异常: {}, 跳过当前批次排序", e.getMessage(), e);
            }

            result.addAll(batch);
        }

        log.info("批量分散重排序完成（兼容模式），处理后SKU总数: {}", result.size());
        return result;
    }

    /**
     * 兼容版本的排序方法
     */
    private List<EsMarketItemInfoDTO> sortSubListLegacy(
        List<EsMarketItemInfoDTO> subList,
        SkuSalesVolumeFunction salesVolumeFunction,
        SkuCategoryFunction categoryFunction) {

        if (CollectionUtils.isEmpty(subList)) {
            return subList;
        }

        // 如果同时提供了类目和销量函数，则按类目分组，组内按销量排序
        if (categoryFunction != null && salesVolumeFunction != null) {
            log.debug("按类目分组，组内按销量倒序排序（兼容模式）");
            return sortByCategoryAndSalesLegacy(subList, categoryFunction, salesVolumeFunction);
        }
        // 如果只提供了销量函数，则仅按销量倒序排序
        else if (salesVolumeFunction != null) {
            log.debug("仅按销量倒序排序（兼容模式）");
            subList.sort((sku1, sku2) -> {
                try {
                    BigDecimal sales1 = salesVolumeFunction.apply(sku1);
                    BigDecimal sales2 = salesVolumeFunction.apply(sku2);
                    return sales2.compareTo(sales1); // 倒序排序，现在不会有null值
                } catch (Exception e) {
                    log.warn("获取SKU销量时出现异常: {}, SKU1: {}, SKU2: {}",
                        e.getMessage(), sku1.getItemCode(), sku2.getItemCode());
                    return 0;
                }
            });
            return subList;
        }
        // 其他情况（例如只有类目函数，或都没有提供）不改变顺序
        return subList;
    }

    /**
     * 兼容版本的按类目和销量排序方法
     */
    private List<EsMarketItemInfoDTO> sortByCategoryAndSalesLegacy(
        List<EsMarketItemInfoDTO> batch,
        SkuCategoryFunction categoryFunction,
        SkuSalesVolumeFunction salesVolumeFunction) {

        if (CollectionUtils.isEmpty(batch)) {
            return batch;
        }

        // 使用LinkedHashMap记录每个类目及其在原始顺序中的位置
        Map<Long, List<EsMarketItemInfoDTO>> categoryGroups = new LinkedHashMap<>();
        List<Long> categoryOrder = new ArrayList<>();

        for (EsMarketItemInfoDTO sku : batch) {
            try {
                Long category = categoryFunction.apply(sku);
                log.debug("category: {}", category);

                // 记录类目及其顺序
                if (!categoryGroups.containsKey(category)) {
                    categoryGroups.put(category, new ArrayList<>());
                    categoryOrder.add(category);
                }
                categoryGroups.get(category).add(sku);

            } catch (Exception e) {
                log.warn("获取SKU类目时出现异常: {}, SKU: {}", e.getMessage(), sku.getItemCode());
                // 如果获取类目失败，将SKU放入默认类目
                Long defaultCategory = -1L;
                if (!categoryGroups.containsKey(defaultCategory)) {
                    categoryGroups.put(defaultCategory, new ArrayList<>());
                    categoryOrder.add(defaultCategory);
                }
                categoryGroups.get(defaultCategory).add(sku);
            }
        }

        // 按原有类目顺序重新组装，每个类目内按销量倒序排序
        List<EsMarketItemInfoDTO> result = new ArrayList<>();
        for (Long category : categoryOrder) {
            List<EsMarketItemInfoDTO> groupSkus = categoryGroups.get(category);
            try {
                // 组内按销量倒序排序
                groupSkus.sort((sku1, sku2) -> {
                    try {
                        BigDecimal sales1 = salesVolumeFunction.apply(sku1);
                        BigDecimal sales2 = salesVolumeFunction.apply(sku2);
                        return sales2.compareTo(sales1); // 倒序排序，现在不会有null值
                    } catch (Exception e) {
                        log.warn("按销量排序时出现异常: {}, SKU1: {}, SKU2: {}",
                            e.getMessage(), sku1.getItemCode(), sku2.getItemCode());
                        return 0;
                    }
                });
            } catch (Exception e) {
                log.warn("按销量排序时出现异常: {}, 类目: {}", e.getMessage(), category);
                // 如果排序失败，保持原有顺序
            }
            result.addAll(groupSkus);
        }

        return result;
    }

    /**
     * 便捷方法：使用默认的库存检查函数进行批量分散重排序
     *
     * @param skuList   待处理的SKU列表
     * @param batchSize 每段SKU数量
     * @return 处理后的SKU列表
     */
    public List<EsMarketItemInfoDTO> batchDisperseSkusWithDefaultStock(
        List<EsMarketItemInfoDTO> skuList,
        int batchSize) {

        SkuStockFunction stockFunction = sku -> {
            return sku.getStoreQuantity() != null && sku.getStoreQuantity() > 0;
        };

        SkuSalesVolumeFunction salesVolumeFunction = sku ->
            skuSalesVolumeCacheService.getSkuSalesVolume(sku.getItemCode());

        SkuCategoryFunction categoryFunction = EsMarketItemInfoDTO::getCategoryId;

        return batchDisperseSkus(skuList, stockFunction, batchSize, salesVolumeFunction, categoryFunction);
    }

    /**
     * 便捷方法：仅按销量进行批量分散重排序（不考虑库存和类目）
     *
     * @param skuList   待处理的SKU列表
     * @param batchSize 每段SKU数量
     * @return 处理后的SKU列表
     */
    public List<EsMarketItemInfoDTO> batchDisperseSkusBySalesOnly(
        List<EsMarketItemInfoDTO> skuList,
        int batchSize) {

        SkuSalesVolumeFunction salesVolumeFunction = sku ->
            skuSalesVolumeCacheService.getSkuSalesVolume(sku.getItemCode());

        return batchDisperseSkus(skuList, null, batchSize, salesVolumeFunction, null);
    }

    /**
     * 便捷方法：仅按库存进行批量分散重排序（不考虑销量和类目）
     *
     * @param skuList   待处理的SKU列表
     * @param batchSize 每段SKU数量
     * @return 处理后的SKU列表
     */
    public List<EsMarketItemInfoDTO> batchDisperseSkusByStockOnly(
        List<EsMarketItemInfoDTO> skuList,
        int batchSize) {

        SkuStockFunction stockFunction = sku -> {
            return sku.getStoreQuantity() != null && sku.getStoreQuantity() > 0;
        };

        return batchDisperseSkus(skuList, stockFunction, batchSize, null, null);
    }
}
