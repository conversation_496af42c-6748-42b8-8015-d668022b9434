package net.summerfarm.mall.service.search;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.common.util.RedisCachePipelineCMD;
import net.summerfarm.mall.common.util.RedisCacheUtil;
import net.xianmu.common.cache.InMemoryCache;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * SKU销售GMV缓存服务 从Redis批量获取SKU的销售GMV数据
 *
 * <AUTHOR> Agent
 */
@Slf4j
@Component
public class SkuSalesVolumeCacheService implements InitializingBean {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private RedisCacheUtil redisCacheUtil;
    /**
     * SKU销售GMV的redis key 前缀
     */
    private static final String SKU_GMV_KEY_PREFIX = "mall_search_rerank_sku_gmv_";

    /**
     * 缓存过期时间（秒）
     */
    private static final long CACHE_EXPIRE_SECONDS = 60 * 30; // 30分钟

    /**
     * 获取单个SKU的销售GMV数据
     *
     * @param sku SKU编码
     * @return 销售GMV，如果不存在则返回BigDecimal.ZERO
     */
    @InMemoryCache(expiryTimeInSeconds = 3600 * 24) // 24小时内存缓存
    public BigDecimal getSkuSalesVolume(String sku) {
        if (StringUtils.isBlank(sku)) {
            return BigDecimal.ZERO;
        }

        String key = SKU_GMV_KEY_PREFIX + sku.trim();
        String gmvStr = redisTemplate.opsForValue().get(key);

        if (StringUtils.isBlank(gmvStr)) {
            log.debug("SKU销售GMV缓存中未找到数据，sku:{}，返回0", sku);
            return BigDecimal.ZERO;
        }

        try {
            return new BigDecimal(gmvStr);
        } catch (NumberFormatException e) {
            log.warn("SKU销售GMV数据格式错误，sku:{}, value:{}，返回0", sku, gmvStr, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 批量获取SKU的销售GMV数据
     *
     * @param skuList SKU编码列表
     * @return SKU编码到销售GMV的映射，key为SKU编码，value为销售GMV
     */
    public Map<String, BigDecimal> getSkuSalesVolumeBatch(Set<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Collections.emptyMap();
        }

        // 过滤空值
        Set<String> validSkuList = skuList.stream()
            .filter(StringUtils::isNotBlank)
            .map(String::trim)
            .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(validSkuList)) {
            return Collections.emptyMap();
        }

        // 使用Redis管道批量查询
        Map<String, String> redisResults = redisCacheUtil.getDataMapWithCachePipelined(
            CACHE_EXPIRE_SECONDS,
            String.class,
            () -> validSkuList.stream()
                .map(sku -> RedisCachePipelineCMD.<String>builder()
                    .key(SKU_GMV_KEY_PREFIX + sku)
                    .value(() -> null) // 默认值取0.0
                    .build())
                .collect(Collectors.toList())
        );

        if (MapUtils.isEmpty(redisResults)) {
            log.warn("批量获取SKU销售GMV数据，Redis中无数据, SKU:{}", validSkuList);
            return Collections.emptyMap();
        }

        // 转换数据类型，为所有请求的SKU提供数据（没有数据的返回0）
        Map<String, BigDecimal> result = Maps.newHashMap();

        // 首先为所有SKU设置默认值0
        for (String sku : validSkuList) {
            result.put(sku, BigDecimal.ZERO);
        }

        // 然后更新有实际数据的SKU
        for (Map.Entry<String, String> entry : redisResults.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            if (StringUtils.isBlank(value)) {
                continue;
            }

            // 从Redis key中提取SKU编码
            String sku = key.replace(SKU_GMV_KEY_PREFIX, "");

            try {
                BigDecimal gmv = new BigDecimal(value);
                result.put(sku, gmv);
            } catch (NumberFormatException e) {
                log.warn("SKU销售GMV数据格式错误，sku:{}, value:{}，使用默认值0", sku, value, e);
                // 保持默认值BigDecimal.ZERO
            }
        }

        log.info("批量获取SKU销售GMV数据完成，请求SKU数量:{}，返回数据:{}", validSkuList.size(), result);
        return result;
    }

    /**
     * 检查SKU是否有销售GMV数据
     *
     * @param sku SKU编码
     * @return true表示有数据，false表示无数据
     */
    public boolean hasSkuSalesVolume(String sku) {
        if (StringUtils.isBlank(sku)) {
            return false;
        }

        String key = SKU_GMV_KEY_PREFIX + sku.trim();
        return redisTemplate.hasKey(key);
    }

    /**
     * 批量检查SKU是否有销售GMV数据
     *
     * @param skuList SKU编码列表
     * @return SKU编码到是否有数据的映射
     */
    public Map<String, Boolean> hasSkuSalesVolumeBatch(Set<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Collections.emptyMap();
        }

        Map<String, Boolean> result = Maps.newHashMap();
        for (String sku : skuList) {
            if (StringUtils.isNotBlank(sku)) {
                result.put(sku, hasSkuSalesVolume(sku));
            }
        }
        return result;
    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }
}
