<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.FrontCategoryMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.FrontCategory">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="outdated" jdbcType="INTEGER" property="outdated" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="relate_type" jdbcType="INTEGER" property="relateType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, parent_id, `name`, outdated, icon, updater, update_time, creator, create_time,relate_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from front_category
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from front_category
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.FrontCategory" useGeneratedKeys="true">
    insert into front_category (parent_id, `name`, outdated, 
      icon, updater, update_time, 
      creator, create_time)
    values (#{parentId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{outdated,jdbcType=INTEGER}, 
      #{icon,jdbcType=VARCHAR}, #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.FrontCategory" useGeneratedKeys="true">
    insert into front_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="outdated != null">
        outdated,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="outdated != null">
        #{outdated,jdbcType=INTEGER},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.FrontCategory">
    update front_category
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="outdated != null">
        outdated = #{outdated,jdbcType=INTEGER},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.FrontCategory">
    update front_category
    set parent_id = #{parentId,jdbcType=INTEGER},
      `name` = #{name,jdbcType=VARCHAR},
      outdated = #{outdated,jdbcType=INTEGER},
      icon = #{icon,jdbcType=VARCHAR},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByCategoryIds" resultType="net.summerfarm.mall.model.vo.FrontCategoryVO">
    select distinct fc.id,
       fc.parent_id parentId,
       fc.name,
       fc.outdated,
       fc.icon,
       fc.updater,
       fc.update_time updateTime,
       fc.creator,
       fc.create_time createTime,
       fcta.priority
      from front_category fc
          left join front_category_to_category fctc on fc.id = fctc.front_category_id
          left join front_category_to_area fcta on fcta.front_category_id = fc.id
      <where>
        <if test="categoryIds != null and categoryIds.size != 0">
          and fctc.category_id in
          <foreach collection="categoryIds" item="cId" open="(" close=")" separator=",">
            #{cId}
          </foreach>
        </if>
        <if test="areaNo != null">
          and area_no = #{areaNo}
        </if>
        and fcta.display = 1
      </where>
      order by fcta.priority desc, fc.id desc
  </select>
  <select id="selectParentFrontCategory" resultType="net.summerfarm.mall.model.vo.FrontCategoryVO">
    select fc.id,
       fc.parent_id   parentId,
       fc.name,
       fc.outdated,
       fc.icon,
       fc.updater,
       fc.update_time updateTime,
       fc.creator,
       fc.create_time createTime,
       fcta.display
    from front_category fc
    join front_category_to_area fcta on fcta.front_category_id = fc.id and fcta.area_no = #{areaNo}
    where fc.outdated = 0
      and fc.parent_id is null
    order by fcta.priority desc, fc.id desc
  </select>
  <select id="selectByParentId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from front_category where parent_id = #{parentId}
  </select>
  <select id="selectBackCategory" resultType="integer">
    select distinct fctc.category_id
    from front_category_to_category fctc
    left join front_category fc on fc.id = fctc.front_category_id
    left join front_category_to_area fcta on fcta.front_category_id = fc.id
    <where>
      and fcta.display = 1
      and area_no = #{areaNo}
      <if test="frontCategoryId != null">
        and fc.parent_id = #{frontCategoryId}
      </if>
    </where>
    order by fcta.priority desc
  </select>

  <resultMap id="FrontCategoryVOResultMap" type="net.summerfarm.mall.model.vo.FrontCategoryVO">
    <id column="id" property="id"/>
    <result column="display" property="display"/>
    <result column="parent_id" property="parentId"/>
    <result column="name" property="name"/>
    <result column="outdated" property="outdated"/>
    <result column="icon" property="icon"/>
    <result column="updater" property="updater"/>
    <result column="update_time" property="updateTime"/>
    <result column="creator" property="creator"/>
    <result column="create_time" property="createTime"/>
  </resultMap>
  <select id="selectByIds" resultMap="FrontCategoryVOResultMap">
    select fc.id, parent_id, name, outdated, icon, fc.updater, fc.update_time, fc.creator, fc.create_time,
        fcta.display
    from front_category fc
        left join front_category_to_area fcta on fcta.front_category_id = fc.id
    where fc.id in
    <foreach item="item" index="index" collection="ids"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach> and area_no = #{areaNo} and outdated = 0 and fcta.display = 1
  </select>

  <select id="selectCategoryByParentIds" resultType="integer">
    select distinct fctc.category_id
    from front_category_to_category fctc
    left join front_category fc on fc.id = fctc.front_category_id
    left join front_category_to_area fcta on fcta.front_category_id = fc.id
    <where>
      and fcta.display = 1
      and fc.outdated = 0
      and fcta.area_no = #{areaNo}
      <if test="ids != null and ids.size != 0">
        and fc.parent_id in
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
    </where>
    order by fcta.priority desc
  </select>
  <select id="selectByCategoryIdsByCategoryIds" resultType="net.summerfarm.mall.model.vo.FrontCategoryCountVO">
    select fc.id,
    fc.parent_id parentId,
    fc.name,
    fctc.category_id categoryId
    from front_category_to_category fctc
    left join front_category fc on fc.id = fctc.front_category_id
    left join front_category_to_area fcta on fcta.front_category_id = fc.id
    <where>
      <if test="categoryIds != null and categoryIds.size != 0">
        and fctc.category_id in
        <foreach collection="categoryIds" item="cId" open="(" close=")" separator=",">
          #{cId}
        </foreach>
      </if>
      <if test="areaNo != null">
        and area_no = #{areaNo}
      </if>
      and fcta.display = 1 and fc.id is not null
    </where>
    GROUP BY fc.id
  </select>

  <select id="selectByIdList" parameterType="java.lang.Integer" resultMap="FrontCategoryVOResultMap">
    select
    <include refid="Base_Column_List" />
    from front_category
    where id in
    <foreach item="item" index="index" collection="list"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

  <select id="selectCategoryMappingByCategoryIds" resultType="net.summerfarm.mall.model.domain.category.FrontCategoryMappingDO">
    select fctc.category_id categoryId,
       fc.parent_id frontCategoryId
      from front_category fc
          left join front_category_to_category fctc on fc.id = fctc.front_category_id
      <where>
        fctc.category_id in
          <foreach collection="categoryIds" item="cId" open="(" close=")" separator=",">
            #{cId}
          </foreach>
      </where>
  </select>
    <select id="selectBySkuRelateType" resultType="net.summerfarm.mall.model.vo.FrontCategoryVO">
      SELECT DISTINCT
      fc.id,
      fc.parent_id AS parentId,
      fc.name,
      fc.outdated,
      fc.icon,
      fc.updater,
      fc.update_time AS updateTime,
      fc.creator,
      fc.create_time AS createTime,
      fcta.priority
      FROM
      front_category fc
      LEFT JOIN front_category_to_sku fcts ON fc.id = fcts.front_category_id
      LEFT JOIN front_category_to_area fcta ON fcta.front_category_id = fc.id
      LEFT JOIN inventory i ON i.sku = fcts.sku
      LEFT JOIN products p ON i.pd_id = p.pd_id
      LEFT JOIN area_sku a ON i.sku = a.sku
      <where>
        fcta.display = 1 and fc.outdated = 0 and fc.relate_type =1 and fc.id is not null
        and a.on_sale = 1
        AND i.outdated = 0
        AND a.show = 1
        and a.m_type = 0
        <if test="areaNo != null">
          and a.area_no = #{areaNo}
        </if>
      </where>
    </select>
</mapper>