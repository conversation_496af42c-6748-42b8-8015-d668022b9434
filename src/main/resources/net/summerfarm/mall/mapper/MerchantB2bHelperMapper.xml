<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MerchantB2bHelperMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.merchant.MerchantB2bHelper">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="b2b_status" jdbcType="TINYINT" property="b2bStatus" />
    <result column="coupon_status" jdbcType="TINYINT" property="couponStatus" />
    <result column="popup_status" jdbcType="TINYINT" property="popupStatus" />
    <result column="cover_status" jdbcType="TINYINT" property="coverStatus" />
    <result column="merchant_info" jdbcType="VARCHAR" property="merchantInfo" />
    <result column="mp_openid" jdbcType="VARCHAR" property="mpOpenid" />

  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, m_id, account_id, b2b_status, coupon_status, popup_status, cover_status,
    merchant_info, mp_openid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_b2b_helper
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_b2b_helper
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.merchant.MerchantB2bHelper" useGeneratedKeys="true">
    insert into merchant_b2b_helper (create_time, update_time, m_id, 
      account_id, b2b_status, coupon_status, 
      popup_status, merchant_info, mp_openid
      )
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{mId,jdbcType=BIGINT}, 
      #{accountId,jdbcType=BIGINT}, #{b2bStatus,jdbcType=TINYINT}, #{couponStatus,jdbcType=TINYINT}, 
      #{popupStatus,jdbcType=TINYINT}, #{merchantInfo,jdbcType=VARCHAR}, #{mpOpenid,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.merchant.MerchantB2bHelper" useGeneratedKeys="true">
    insert into merchant_b2b_helper
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="b2bStatus != null">
        b2b_status,
      </if>
      <if test="couponStatus != null">
        coupon_status,
      </if>
      <if test="popupStatus != null">
        popup_status,
      </if>
      <if test="merchantInfo != null">
        merchant_info,
      </if>
      <if test="mpOpenid != null">
        mp_openid,
      </if>
      <if test="coverStatus != null">
        cover_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="b2bStatus != null">
        #{b2bStatus,jdbcType=TINYINT},
      </if>
      <if test="couponStatus != null">
        #{couponStatus,jdbcType=TINYINT},
      </if>
      <if test="popupStatus != null">
        #{popupStatus,jdbcType=TINYINT},
      </if>
      <if test="merchantInfo != null">
        #{merchantInfo,jdbcType=VARCHAR},
      </if>
      <if test="mpOpenid != null">
        #{mpOpenid,jdbcType=VARCHAR},
      </if>
      <if test="coverStatus != null">
        #{coverStatus,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.merchant.MerchantB2bHelper">
    update merchant_b2b_helper
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="b2bStatus != null">
        b2b_status = #{b2bStatus,jdbcType=TINYINT},
      </if>
      <if test="couponStatus != null">
        coupon_status = #{couponStatus,jdbcType=TINYINT},
      </if>
      <if test="popupStatus != null">
        popup_status = #{popupStatus,jdbcType=TINYINT},
      </if>
      <if test="merchantInfo != null">
        merchant_info = #{merchantInfo,jdbcType=VARCHAR},
      </if>
      <if test="mpOpenid != null">
        mp_openid = #{mpOpenid,jdbcType=VARCHAR},
      </if>
      <if test="coverStatus != null">
        cover_status = #{coverStatus,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.merchant.MerchantB2bHelper">
    update merchant_b2b_helper
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      m_id = #{mId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=BIGINT},
      b2b_status = #{b2bStatus,jdbcType=TINYINT},
      coupon_status = #{couponStatus,jdbcType=TINYINT},
      popup_status = #{popupStatus,jdbcType=TINYINT},
      merchant_info = #{merchantInfo,jdbcType=VARCHAR},
      mp_openid = #{mpOpenid,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByMidAccountId"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_b2b_helper
    where m_id = #{mId} and account_id = #{accountId}
  </select>
</mapper>